/**
 * Chart Library Fix
 * This script ensures that chart libraries are properly loaded and available globally
 */

(function() {
    let librariesLoaded = false;

    function checkAndLoadLibraries() {
        // Check if ApexCharts is available
        if (typeof ApexCharts === 'undefined' && typeof window.ApexCharts !== 'undefined') {
            console.log('Making ApexCharts globally available');
            window.ApexCharts = window.ApexCharts;
        }

        // Check if echarts is available
        if (typeof echarts === 'undefined' && typeof window.echarts !== 'undefined') {
            console.log('Making echarts globally available');
            window.echarts = window.echarts;
        }

        // Check if d3 is available
        if (typeof d3 === 'undefined' && typeof window.d3 !== 'undefined') {
            console.log('Making d3 globally available');
            window.d3 = window.d3;
        }

        // Log the status of chart libraries
        console.log('Chart libraries status:');
        console.log('- ApexCharts available:', typeof window.ApexCharts !== 'undefined');
        console.log('- ECharts available:', typeof window.echarts !== 'undefined');
        console.log('- D3 available:', typeof window.d3 !== 'undefined');

        // If libraries are not available, try to load them
        const promises = [];

        if (typeof window.ApexCharts === 'undefined') {
            console.warn('ApexCharts not found, attempting to load it');

            const promise = new Promise((resolve, reject) => {
                const apexScript = document.createElement('script');
                apexScript.src = 'lib/apexcharts.min.js';
                apexScript.onload = function() {
                    console.log('ApexCharts loaded successfully');
                    resolve();
                };
                apexScript.onerror = function() {
                    console.error('Failed to load ApexCharts');
                    reject(new Error('Failed to load ApexCharts'));
                };
                document.head.appendChild(apexScript);
            });
            promises.push(promise);
        }

        if (typeof window.echarts === 'undefined') {
            console.warn('ECharts not found, attempting to load it');

            const promise = new Promise((resolve, reject) => {
                const echartsScript = document.createElement('script');
                echartsScript.src = 'lib/echarts.js';
                echartsScript.onload = function() {
                    console.log('ECharts loaded successfully');
                    resolve();
                };
                echartsScript.onerror = function() {
                    console.error('Failed to load ECharts');
                    reject(new Error('Failed to load ECharts'));
                };
                document.head.appendChild(echartsScript);
            });
            promises.push(promise);
        }

        if (typeof window.d3 === 'undefined') {
            console.warn('D3 not found, attempting to load it');

            const promise = new Promise((resolve, reject) => {
                const d3Script = document.createElement('script');
                d3Script.src = 'lib/d3.min.js';
                d3Script.onload = function() {
                    console.log('D3 loaded successfully');
                    resolve();
                };
                d3Script.onerror = function() {
                    console.error('Failed to load D3');
                    reject(new Error('Failed to load D3'));
                };
                document.head.appendChild(d3Script);
            });
            promises.push(promise);
        }

        return Promise.allSettled(promises).then(() => {
            librariesLoaded = true;
            console.log('Chart library loading completed');

            // Dispatch a custom event to notify that libraries are ready
            window.dispatchEvent(new CustomEvent('chartLibrariesReady', {
                detail: {
                    apexCharts: typeof window.ApexCharts !== 'undefined',
                    echarts: typeof window.echarts !== 'undefined',
                    d3: typeof window.d3 !== 'undefined'
                }
            }));
        });
    }

    // Make the function available globally
    window.ensureChartLibrariesLoaded = function() {
        if (librariesLoaded) {
            return Promise.resolve();
        }
        return checkAndLoadLibraries();
    };

    // Initial check
    checkAndLoadLibraries();
})();
