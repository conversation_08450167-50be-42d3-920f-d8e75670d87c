<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="script-src 'self' 'wasm-unsafe-eval';">
    <title>Enven Bridge Sheet</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/menu.css">
    <link rel="stylesheet" href="css/excel-table.css">
    <link rel="stylesheet" href="css/chart-dialog.css">
    <link rel="stylesheet" href="css/improved-chart-dialog.css">
    <link rel="stylesheet" href="css/welcome-modal.css">
    <link rel="stylesheet" href="css/sheet-tabs.css">
    <link rel="stylesheet" href="css/color-palette.css">
    <link rel="stylesheet" href="css/clipboard.css">
    <link rel="stylesheet" href="css/modal.css">
    <link rel="stylesheet" href="css/library-demo.css">
    <link rel="stylesheet" href="css/standardized-modal.css">
    <link rel="stylesheet" href="css/modern-modal.css">
    <link rel="stylesheet" href="css/acumatica-panel.css">
    <link rel="stylesheet" href="css/chart-creation-dialog.css">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Standardized modal styles -->
    <style>
        /* Ensure all modals have consistent styling */
        .standard-modal-overlay, .modal, .modal-overlay, .excel-modal, .welcome-overlay, .chart-dialog, .excel-modal.chart-dialog {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            background: rgba(0, 0, 0, 0.5) !important;
            z-index: 9999 !important;
            overflow: hidden !important;
            margin: 0 !important;
            padding: 0 !important;
            box-sizing: border-box !important;
        }

        /* Vertical tool hover effects */
        .vertical-tool:hover {
            background-color: #e8f0fe;
        }

        .vertical-tool:hover .material-icons,
        .vertical-tool:hover span {
            color: #1a73e8;
            transform: scale(1.1);
        }

        .vertical-tool:active {
            background-color: #d2e3fc;
        }

        /* Clipboard styles moved to clipboard.css */

        /* Ensure welcome footer text is centered */
        .welcome-footer {
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
        }

        .welcome-footer p {
            text-align: center !important;
            width: 100% !important;
        }

        /* Ensure chart modal is properly displayed */
        .excel-modal.chart-dialog,
        #chart-modal-overlay,
        .standard-modal-overlay.chart-dialog {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            background: rgba(0, 0, 0, 0.5) !important;
            z-index: 9999 !important;
            overflow: hidden !important;
            margin: 0 !important;
            padding: 0 !important;
            box-sizing: border-box !important;
            min-width: 100vw !important;
            min-height: 100vh !important;
        }

        /* Fix for body when modal is open */
        body.modal-open {
            overflow: hidden !important;
            margin: 0 !important;
            padding: 0 !important;
            height: 100vh !important;
            width: 100vw !important;
        }
    </style>
</head>
<body class="full-screen">
    <div class="app-title">
        <span class="material-icons">table_chart</span>
        <h1>Enven Bridge Sheet</h1>
    </div>

    <div class="menu-bar">
        <div class="menu-item"><span>File</span>
            <div class="submenu">
                <div class="submenu-item" id="newWorkbookBtn"><span class="material-icons">add</span> New Workbook</div>
                <div class="submenu-item" id="openFileBtn"><span class="material-icons">folder_open</span> Open</div>
                <div class="submenu-separator"></div>
                <div class="submenu-item" id="saveFileBtn"><span class="material-icons">save</span> Save</div>
                <div class="submenu-item" id="saveAsFileBtn"><span class="material-icons">save_as</span> Save As</div>
                <div class="submenu-item" id="exportFileBtn"><span class="material-icons">cloud_download</span> Export</div>
                <div class="submenu-separator"></div>
                <div class="submenu-item" id="printBtn"><span class="material-icons">print</span> Print</div>
                <div class="submenu-separator"></div>
                <div class="submenu-item" id="pageSetupBtn"><span class="material-icons">settings_applications</span> Page Setup</div>
                <div class="submenu-item" id="workbookSettingsBtn"><span class="material-icons">settings</span> Workbook Settings</div>
                <div class="submenu-separator"></div>
                <div class="submenu-item" id="libraryDemoBtn"><span class="material-icons">library_books</span> Library Demo</div>
            </div>
        </div>
        <div class="menu-item"><span>Edit</span>
            <div class="submenu">
                <div class="submenu-item">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 18px; height: 18px; margin-right: 8px;">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M7.53033 3.46967C7.23744 3.17678 6.76256 3.17678 6.46967 3.46967L3.46967 6.46967C3.17678 6.76256 3.17678 7.23744 3.46967 7.53033L6.46967 10.5303C6.76256 10.8232 7.23744 10.8232 7.53033 10.5303C7.82322 10.2374 7.82322 9.76256 7.53033 9.46967L5.06066 7L7.53033 4.53033C7.82322 4.23744 7.82322 3.76256 7.53033 3.46967Z" fill="#5F6368"></path>
                            <path opacity="0.5" d="M5.81066 6.25H15C18.1756 6.25 20.75 8.82436 20.75 12C20.75 15.1756 18.1756 17.75 15 17.75H8C7.58579 17.75 7.25 17.4142 7.25 17C7.25 16.5858 7.58579 16.25 8 16.25H15C17.3472 16.25 19.25 14.3472 19.25 12C19.25 9.65279 17.3472 7.75 15 7.75H5.81066L5.06066 7L5.81066 6.25Z" fill="#5F6368"></path>
                        </g>
                    </svg>
                    Undo
                </div>
                <div class="submenu-item">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 18px; height: 18px; margin-right: 8px;">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <path opacity="0.4" d="M16.8691 18.3101H8.86914C6.10914 18.3101 3.86914 16.0701 3.86914 13.3101C3.86914 10.5501 6.10914 8.31006 8.86914 8.31006H19.8691" stroke="#5F6368" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M17.5703 10.8099L20.1303 8.24994L17.5703 5.68994" stroke="#5F6368" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        </g>
                    </svg>
                    Redo
                </div>
                <div class="submenu-separator"></div>
                <div class="submenu-item"><span class="material-icons">content_cut</span> Cut</div>
                <div class="submenu-item"><span class="material-icons">content_copy</span> Copy</div>
                <div class="submenu-item"><span class="material-icons">content_paste</span> Paste</div>
                <div class="submenu-item"><span class="material-icons">paste_special</span> Paste Special</div>
                <div class="submenu-separator"></div>
                <div class="submenu-item"><span class="material-icons">search</span> Find and Replace</div>
                <div class="submenu-item"><span class="material-icons">clear</span> Clear Contents</div>
                <div class="submenu-item"><span class="material-icons">select_all</span> Select All</div>
            </div>
        </div>
        <div class="menu-item"><span>View</span>
            <div class="submenu">
                <div class="submenu-item"><span class="material-icons">vertical_align_center</span> Freeze Panes</div>
                <div class="submenu-item"><span class="material-icons">zoom_in</span> Zoom In</div>
                <div class="submenu-item"><span class="material-icons">zoom_out</span> Zoom Out</div>
                <div class="submenu-item"><span class="material-icons">fit_screen</span> Fit to Screen</div>
                <div class="submenu-separator"></div>
                <div class="submenu-item"><span class="material-icons">grid_on</span> Show/Hide Gridlines</div>
                <div class="submenu-item"><span class="material-icons">functions</span> Show/Hide Formula Bar</div>
                <div class="submenu-item"><span class="material-icons">view_column</span> Show/Hide Headers</div>
                <div class="submenu-separator"></div>
                <div class="submenu-item"><span class="material-icons">fullscreen</span> Fullscreen Mode</div>
                <div class="submenu-item"><span class="material-icons">view_agenda</span> Split View</div>
                <div class="submenu-item"><span class="material-icons">dark_mode</span> Dark/Light Theme</div>
            </div>
        </div>
        <div class="menu-item"><span>Insert</span>
            <div class="submenu">
                <div class="submenu-item"><span class="material-icons">add_row</span> Insert Row Above</div>
                <div class="submenu-item"><span class="material-icons">add_row</span> Insert Row Below</div>
                <div class="submenu-item"><span class="material-icons">add_column</span> Insert Column Left</div>
                <div class="submenu-item"><span class="material-icons">add_column</span> Insert Column Right</div>
                <div class="submenu-item"><span class="material-icons">tab</span> Insert Sheet</div>
                <div class="submenu-separator"></div>
                <div class="submenu-item"><span class="material-icons">add_chart</span> Insert Chart</div>
                <div class="submenu-item"><span class="material-icons">table_chart</span> Insert Table</div>
                <div class="submenu-item"><span class="material-icons">image</span> Insert Image</div>
                <div class="submenu-item"><span class="material-icons">link</span> Insert Hyperlink</div>
                <div class="submenu-separator"></div>
                <div class="submenu-item"><span class="material-icons">arrow_drop_down_circle</span> Insert Dropdown</div>
                <div class="submenu-item"><span class="material-icons">check_box</span> Insert Checkbox</div>
                <div class="submenu-item"><span class="material-icons">event</span> Insert Date Picker</div>
                <div class="submenu-item"><span class="material-icons">smart_button</span> Insert Button</div>
                <div class="submenu-item"><span class="material-icons">comment</span> Insert Comment</div>
            </div>
        </div>
        <div class="menu-item"><span>Format</span>
            <div class="submenu">
                <div class="submenu-item"><span class="material-icons">font_download</span> Font Style</div>
                <div class="submenu-item"><span class="material-icons">format_size</span> Font Size</div>
                <div class="submenu-item"><span class="material-icons">format_color_text</span> Font Color</div>
                <div class="submenu-separator"></div>
                <div class="submenu-item"><span class="material-icons">format_color_fill</span> Cell Background</div>
                <div class="submenu-item"><span class="material-icons">border_all</span> Border Styles</div>
                <div class="submenu-separator"></div>
                <div class="submenu-item"><span class="material-icons">format_align_left</span> Horizontal Alignment</div>
                <div class="submenu-item"><span class="material-icons">vertical_align_center</span> Vertical Alignment</div>
                <div class="submenu-item"><span class="material-icons">wrap_text</span> Text Wrapping</div>
                <div class="submenu-item"><span class="material-icons">rotate_90_degrees_ccw</span> Text Rotation</div>
                <div class="submenu-separator"></div>
                <div class="submenu-item"><span class="material-icons">123</span> Number Format</div>
                <div class="submenu-item">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 18px; height: 18px; margin-right: 8px;">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <path d="M6.75 6.79904L6.375 7.44856L6.75 6.79904ZM6.20096 6.25L5.55144 6.625L6.20096 6.25ZM17.799 6.25L18.4486 6.625L17.799 6.25ZM17.25 6.79904L17.625 7.44856L17.25 6.79904ZM17.25 2.20096L17.625 1.55144L17.25 2.20096ZM17.799 2.75L18.4486 2.375L17.799 2.75ZM6.75 2.20096L6.375 1.55144L6.75 2.20096ZM6.20096 2.75L5.55144 2.375L6.20096 2.75ZM13.7071 21.7071L13.1768 21.1768L13.7071 21.7071ZM13.7071 14.2929L13.1768 14.8232L13.7071 14.2929ZM10.2929 14.2929L9.76256 13.7626H9.76256L10.2929 14.2929ZM10.2929 21.7071L10.8232 21.1768L10.2929 21.7071ZM12.0047 14.75C12.4189 14.75 12.7547 14.4142 12.7547 14C12.7547 13.5858 12.4189 13.25 12.0047 13.25V14.75ZM8.5 2.75H15.5V1.25H8.5V2.75ZM15.5 6.25H8.5V7.75H15.5V6.25ZM8.5 6.25C8.01889 6.25 7.7082 6.24928 7.47275 6.22794C7.2476 6.20754 7.16586 6.17311 7.125 6.14952L6.375 7.44856C6.68221 7.62593 7.00817 7.69198 7.33735 7.72182C7.65622 7.75072 8.04649 7.75 8.5 7.75V6.25ZM5.25 4.5C5.25 4.95351 5.24928 5.34378 5.27818 5.66265C5.30802 5.99183 5.37407 6.31779 5.55144 6.625L6.85048 5.875C6.82689 5.83414 6.79246 5.7524 6.77206 5.52725C6.75072 5.2918 6.75 4.98111 6.75 4.5H5.25ZM7.125 6.14952C7.01099 6.08369 6.91631 5.98901 6.85048 5.875L5.55144 6.625C5.74892 6.96704 6.03296 7.25108 6.375 7.44856L7.125 6.14952ZM17.25 4.5C17.25 4.98111 17.2493 5.2918 17.2279 5.52725C17.2075 5.7524 17.1731 5.83414 17.1495 5.875L18.4486 6.625C18.6259 6.31779 18.692 5.99183 18.7218 5.66265C18.7507 5.34378 18.75 4.95351 18.75 4.5H17.25ZM15.5 7.75C15.9535 7.75 16.3438 7.75072 16.6627 7.72182C16.9918 7.69198 17.3178 7.62593 17.625 7.44856L16.875 6.14952C16.8341 6.17311 16.7524 6.20754 16.5273 6.22794C16.2918 6.24928 15.9811 6.25 15.5 6.25V7.75ZM17.1495 5.875C17.0837 5.98901 16.989 6.08369 16.875 6.14952L17.625 7.44856C17.967 7.25108 18.2511 6.96704 18.4486 6.625L17.1495 5.875ZM15.5 2.75C15.9811 2.75 16.2918 2.75072 16.5273 2.77206C16.7524 2.79246 16.8341 2.82689 16.875 2.85048L17.625 1.55144C17.3178 1.37407 16.9918 1.30802 16.6627 1.27818C16.3438 1.24928 15.9535 1.25 15.5 1.25V2.75ZM18.75 4.5C18.75 4.04649 18.7507 3.65622 18.7218 3.33735C18.692 3.00817 18.6259 2.68221 18.4486 2.375L17.1495 3.125C17.1731 3.16586 17.2075 3.2476 17.2279 3.47275C17.2493 3.7082 17.25 4.01889 17.25 4.5H18.75ZM16.875 2.85048C16.989 2.91631 17.0837 3.01099 17.1495 3.125L18.4486 2.375C18.2511 2.03296 17.967 1.74892 17.625 1.55144L16.875 2.85048ZM8.5 1.25C8.04649 1.25 7.65622 1.24928 7.33735 1.27818C7.00817 1.30802 6.68221 1.37407 6.375 1.55144L7.125 2.85048C7.16586 2.82689 7.2476 2.79246 7.47275 2.77206C7.7082 2.75072 8.01889 2.75 8.5 2.75V1.25ZM6.75 4.5C6.75 4.01889 6.75072 3.7082 6.77206 3.47275C6.79246 3.2476 6.82689 3.16586 6.85048 3.125L5.55144 2.375C5.37407 2.68221 5.30802 3.00817 5.27818 3.33735C5.24928 3.65622 5.25 4.04649 5.25 4.5H6.75ZM6.375 1.55144C6.03296 1.74892 5.74892 2.03296 5.55144 2.375L6.85048 3.125C6.91631 3.01099 7.01099 2.91631 7.125 2.85048L6.375 1.55144ZM10.75 20V16H9.25V20H10.75ZM13.25 16V20H14.75V20H13.25ZM13.25 20C13.25 20.4926 13.2484 20.7866 13.2201 20.9973C13.2071 21.0939 13.1918 21.1423 13.1828 21.164C13.1808 21.1691 13.1791 21.1724 13.1781 21.1743C13.1771 21.1762 13.1766 21.1771 13.1765 21.1772C13.1765 21.1772 13.1766 21.177 13.1769 21.1766C13.1772 21.1763 13.1772 21.1763 13.1768 21.1768L14.2374 22.2374C14.5465 21.9284 14.6589 21.5527 14.7067 21.1972C14.7516 20.8633 14.75 20.4502 14.75 20H13.25ZM12 22.75C12.4502 22.75 12.8633 22.7516 13.1972 22.7067C13.5527 22.6589 13.9284 22.5465 14.2374 22.2374L13.1768 21.1768C13.1763 21.1772 13.1763 21.1772 13.1767 21.1769C13.177 21.1766 13.1772 21.1765 13.1772 21.1765C13.1771 21.1766 13.1762 21.1771 13.1743 21.1781C13.1724 21.1791 13.1691 21.1808 13.164 21.1828C13.1423 21.1918 13.0939 21.2071 12.9973 21.2201C12.7866 21.2484 12.4926 21.25 12 21.25V22.75ZM12 14.75C12.4926 14.75 12.7866 14.7516 12.9973 14.7799C13.0939 14.7929 13.1423 14.8082 13.164 14.8172C13.1691 14.8192 13.1724 14.8209 13.1743 14.8219C13.1762 14.8229 13.1771 14.8234 13.1772 14.8235C13.1772 14.8235 13.177 14.8234 13.1767 14.8231C13.1763 14.8228 13.1763 14.8228 13.1768 14.8232L14.2374 13.7626C13.9284 13.4535 13.5527 13.3411 13.1972 13.2933C12.8633 13.2484 12.4502 13.25 12 13.25V14.75ZM14.75 16C14.75 15.5498 14.7516 15.1367 14.7067 14.8028C14.6589 14.4473 14.5465 14.0716 14.2374 13.7626L13.1768 14.8232C13.1772 14.8237 13.1772 14.8237 13.1769 14.8234C13.1766 14.823 13.1765 14.8228 13.1765 14.8228C13.1766 14.8229 13.1771 14.8238 13.1781 14.8257C13.1791 14.8276 13.1808 14.8309 13.1828 14.836C13.1918 14.8577 13.2071 14.9061 13.2201 15.0027C13.2484 15.2134 13.25 15.5074 13.25 16H14.75ZM10.75 16C10.75 15.5074 10.7516 15.2134 10.7799 15.0027C10.7929 14.9061 10.8082 14.8577 10.8172 14.836C10.8192 14.8309 10.8209 14.8276 10.8219 14.8257C10.8229 14.8238 10.8234 14.8229 10.8235 14.8228C10.8235 14.8228 10.8234 14.823 10.8231 14.8234C10.8228 14.8237 10.8228 14.8237 10.8232 14.8232L9.76256 13.7626C9.45354 14.0716 9.34109 14.4473 9.2933 14.8028C9.24841 15.1367 9.25 15.5498 9.25 16H10.75ZM12 13.25C11.5498 13.25 11.1367 13.2484 10.8028 13.2933C10.4473 13.3411 10.0716 13.4535 9.76256 13.7626L10.8232 14.8232C10.8237 14.8228 10.8237 14.8228 10.8234 14.8231C10.823 14.8234 10.8228 14.8235 10.8228 14.8235C10.8229 14.8234 10.8238 14.8229 10.8257 14.8219C10.8276 14.8209 10.8309 14.8192 10.836 14.8172C10.8577 14.8082 10.9061 14.7929 11.0027 14.7799C11.2134 14.7516 11.5074 14.75 12 14.75V13.25ZM9.25 20C9.25 20.4502 9.24841 20.8633 9.2933 21.1972C9.34109 21.5527 9.45354 21.9284 9.76256 22.2374L10.8232 21.1768C10.8228 21.1763 10.8228 21.1763 10.8231 21.1766C10.8234 21.177 10.8235 21.1772 10.8235 21.1772C10.8234 21.1771 10.8229 21.1762 10.8219 21.1743C10.8209 21.1724 10.8192 21.1691 10.8172 21.164C10.8082 21.1423 10.7929 21.0939 10.7799 20.9973C10.7516 20.7866 10.75 20.4926 10.75 20H9.25ZM12 21.25C11.5074 21.25 11.2134 21.2484 11.0027 21.2201C10.9061 21.2071 10.8577 21.1918 10.836 21.1828C10.8309 21.1808 10.8276 21.1791 10.8257 21.1781C10.8238 21.1771 10.8229 21.1766 10.8228 21.1765C10.8228 21.1765 10.823 21.1766 10.8234 21.1769C10.8237 21.1772 10.8237 21.1772 10.8232 21.1768L9.76256 22.2374C10.0716 22.5465 10.4473 22.6589 10.8028 22.7067C11.1367 22.7516 11.5498 22.75 12 22.75V21.25ZM12 14.75H12.0047V13.25H12V14.75Z" fill="#5F6368"></path>
                            <path opacity="0.5" d="M5.5 3.75C5.08579 3.75 4.75 4.08579 4.75 4.5C4.75 4.91421 5.08579 5.25 5.5 5.25V3.75ZM6 3.75H5.5V5.25H6V3.75Z" fill="#5F6368"></path>
                            <path opacity="0.5" d="M15.4068 10.989L15.2956 10.2473L15.4068 10.989ZM19.4834 10.3775L19.3722 9.63581L19.4834 10.3775ZM21.8611 5.76733L22.559 5.49258L22.559 5.49258L21.8611 5.76733ZM20.7329 4.63903L20.4581 5.3369L20.7329 4.63903ZM20.9386 10.0438L20.5867 9.38148V9.38148L20.9386 10.0438ZM21.886 8.94369L22.5932 9.19346V9.19346L21.886 8.94369ZM12.4847 11.9173L11.9164 11.4278L11.9164 11.4278L12.4847 11.9173ZM11.255 13.9875C11.2481 14.4016 11.5782 14.743 11.9923 14.7499C12.4065 14.7568 12.7479 14.4267 12.7548 14.0125L11.255 13.9875ZM15.5181 11.7307L19.5947 11.1192L19.3722 9.63581L15.2956 10.2473L15.5181 11.7307ZM19.0451 3.75H18.0002V5.25H19.0451V3.75ZM22.7502 7.4551C22.7502 7.02002 22.7506 6.65783 22.7312 6.3612C22.7115 6.05823 22.6689 5.77171 22.559 5.49258L21.1633 6.04208C21.1924 6.11609 21.2194 6.22858 21.2344 6.45878C21.2498 6.6953 21.2502 7.00044 21.2502 7.4551H22.7502ZM19.0451 5.25C19.4997 5.25 19.8049 5.25037 20.0414 5.26579C20.2716 5.2808 20.3841 5.30776 20.4581 5.3369L21.0076 3.94117C20.7285 3.83128 20.442 3.78872 20.139 3.76897C19.8423 3.74963 19.4802 3.75 19.0451 3.75V5.25ZM22.559 5.49258C22.2795 4.78261 21.7176 4.22069 21.0076 3.94117L20.4581 5.3369C20.7808 5.46395 21.0362 5.71937 21.1633 6.04208L22.559 5.49258ZM19.5947 11.1192C20.3032 11.0129 20.8462 10.9422 21.2905 10.7061L20.5867 9.38148C20.4256 9.4671 20.2002 9.5116 19.3722 9.63581L19.5947 11.1192ZM21.2502 7.4551C21.2502 8.29243 21.2396 8.52185 21.1788 8.69391L22.5932 9.19346C22.7608 8.71904 22.7502 8.17157 22.7502 7.4551H21.2502ZM21.2905 10.7061C21.8989 10.3829 22.3638 9.84304 22.5932 9.19346L21.1788 8.69391C21.0745 8.98918 20.8632 9.23455 20.5867 9.38148L21.2905 10.7061ZM15.2956 10.2473C14.5023 10.3663 13.8378 10.4646 13.315 10.6116C12.772 10.7643 12.2915 10.9923 11.9164 11.4278L13.053 12.4067C13.1625 12.2796 13.3305 12.1654 13.7211 12.0556C14.132 11.94 14.6863 11.8555 15.5181 11.7307L15.2956 10.2473ZM12.7548 14.0125C12.7726 12.9469 12.8715 12.6175 13.053 12.4067L11.9164 11.4278C11.3356 12.1023 11.2719 12.9787 11.255 13.9875L12.7548 14.0125Z" fill="#5F6368"></path>
                        </g>
                    </svg>
                    Conditional Formatting
                </div>
                <div class="submenu-item"><span class="material-icons">call_merge</span> Merge Cells</div>
            </div>
        </div>
        <div class="menu-item"><span>Data</span>
            <div class="submenu">
                <div class="submenu-item"><span class="material-icons">sort</span> Sort Ascending</div>
                <div class="submenu-item"><span class="material-icons">sort</span> Sort Descending</div>
                <div class="submenu-item"><span class="material-icons">filter_list</span> Filter</div>
                <div class="submenu-separator"></div>
                <div class="submenu-item"><span class="material-icons">rule</span> Data Validation</div>
                <div class="submenu-item"><span class="material-icons">view_column</span> Split Text to Columns</div>
                <div class="submenu-item"><span class="material-icons">remove_circle</span> Remove Duplicates</div>
                <div class="submenu-separator"></div>
                <div class="submenu-item"><span class="material-icons">pivot_table_chart</span> Pivot Table</div>
                <div class="submenu-item"><span class="material-icons">group_work</span> Group Rows</div>
                <div class="submenu-item"><span class="material-icons">layers_clear</span> Ungroup Rows</div>
                <div class="submenu-item"><span class="material-icons">label</span> Named Ranges</div>
            </div>
        </div>
        <div class="menu-item"><span>Tools</span>
            <div class="submenu">
                <div class="submenu-item"><span class="material-icons">psychology</span> Formula Assistant</div>
                <div class="submenu-item"><span class="material-icons">code</span> Script Editor</div>
                <div class="submenu-item"><span class="material-icons">smart_button</span> Macros</div>
                <div class="submenu-separator"></div>
                <div class="submenu-item"><span class="material-icons">merge_type</span> Data Merge Tool</div>
                <div class="submenu-item"><span class="material-icons">history</span> Audit Logs</div>
                <div class="submenu-item"><span class="material-icons">api</span> OData Settings</div>
                <div class="submenu-separator"></div>
                <div class="submenu-item"><span class="material-icons">extension</span> Acumatica Panel</div>
                <div class="submenu-item"><span class="material-icons">extension</span> Monday.com Panel</div>
                <div class="submenu-item"><span class="material-icons">schema</span> Data Mapper</div>
                <div class="submenu-separator"></div>
                <div class="submenu-item"><span class="material-icons">insert_chart</span> Chart Builder</div>
                <div class="submenu-item"><span class="material-icons">auto_fix_high</span> Workflow Automation</div>
                <div class="submenu-item"><span class="material-icons">event</span> Cell Event Triggers</div>
                <div class="submenu-item"><span class="material-icons">notifications</span> Notification Rules</div>
            </div>
        </div>
        <div class="menu-item"><span>Add-ons</span>
            <div class="submenu">
                <div class="submenu-item"><span class="material-icons">extension</span> Plugin Manager</div>
                <div class="submenu-item"><span class="material-icons">cloud_download</span> Load External Modules</div>
                <div class="submenu-item"><span class="material-icons">view_sidebar</span> Custom Sidebar Tools</div>
                <div class="submenu-separator"></div>
                <div class="submenu-item" id="acumaticaConnectorBtn"><span class="material-icons">extension</span> Acumatica Connector</div>
                <div class="submenu-item"><span class="material-icons">extension</span> Monday.com Connector</div>
                <div class="submenu-item"><span class="material-icons">cloud</span> OData Connection</div>
                <div class="submenu-separator"></div>
                <div class="submenu-item"><span class="material-icons">api</span> Cloud API Connector</div>
            </div>
        </div>
        <div class="menu-item"><span>Help</span>
            <div class="submenu">
                <div class="submenu-item"><span class="material-icons">description</span> Documentation</div>
                <div class="submenu-item"><span class="material-icons">keyboard</span> Keyboard Shortcuts</div>
                <div class="submenu-separator"></div>
                <div class="submenu-item"><span class="material-icons">build</span> Troubleshooting Panel</div>
                <div class="submenu-item"><span class="material-icons">bug_report</span> Feedback and Bug Report</div>
                <div class="submenu-separator"></div>
                <div class="submenu-item"><span class="material-icons">info</span> About This App</div>
                <div class="submenu-item"><span class="material-icons">support</span> Support</div>
            </div>
        </div>
    </div>

    <div id="toolbar" style="padding: 20px 6px;">
        <!-- Past Notes Box and Clipboard Tools in a single group -->
        <div class="toolbar-group" role="group" aria-label="Past and Clipboard Tools" style="display: flex; align-items: center; gap: 8px; margin-bottom: 2px;">
            <!-- Past Notes Box -->
            <div class="past-notes-box" style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 56px; width: 42px; margin-top: 2px; margin-bottom: 2px;">
                <svg viewBox="0 0 24.00 24.00" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#000000" stroke-width="0.024" style="width: 32px; height: 32px;">
                    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                    <g id="SVGRepo_iconCarrier">
                        <path opacity="0.4" d="M16.2391 3.6499H7.75906C5.28906 3.6499 3.28906 5.6599 3.28906 8.1199V17.5299C3.28906 19.9899 5.29906 21.9999 7.75906 21.9999H16.2291C18.6991 21.9999 20.6991 19.9899 20.6991 17.5299V8.1199C20.7091 5.6499 18.6991 3.6499 16.2391 3.6499Z" fill="#e6c700"></path>
                        <path d="M14.3498 2H9.64977C8.60977 2 7.75977 2.84 7.75977 3.88V4.82C7.75977 5.86 8.59977 6.7 9.63977 6.7H14.3498C15.3898 6.7 16.2298 5.86 16.2298 4.82V3.88C16.2398 2.84 15.3898 2 14.3498 2Z" fill="#e6c700"></path>
                        <path d="M15 12.9502H8C7.59 12.9502 7.25 12.6102 7.25 12.2002C7.25 11.7902 7.59 11.4502 8 11.4502H15C15.41 11.4502 15.75 11.7902 15.75 12.2002C15.75 12.6102 15.41 12.9502 15 12.9502Z" fill="#e6c700"></path>
                        <path d="M12.38 16.9502H8C7.59 16.9502 7.25 16.6102 7.25 16.2002C7.25 15.7902 7.59 15.4502 8 15.4502H12.38C12.79 15.4502 13.13 15.7902 13.13 16.2002C13.13 16.6102 12.79 16.9502 12.38 16.9502Z" fill="#e6c700"></path>
                    </g>
                </svg>
                <span style="font-size: 12px; margin-top: 2px; color: #5f6368;">Past</span>
            </div>

            <!-- Clipboard Tools - No Box Design -->
            <div style="display: flex; flex-direction: column; height: 56px; width: 32px; margin-top: 2px; margin-bottom: 2px; gap: 2px;">
                <!-- Copy Tool -->
                <div id="copyBtn" class="vertical-tool" style="display: flex; align-items: center; justify-content: center; height: 33.33%; cursor: pointer; transition: all 0.2s;" title="Copy (Ctrl+C)">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 18px; height: 18px;">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <path opacity="0.4" d="M15.5 13.15H13.33C11.55 13.15 10.1 11.71 10.1 9.92V7.75C10.1 7.34 9.77 7 9.35 7H6.18C3.87 7 2 8.5 2 11.18V17.82C2 20.5 3.87 22 6.18 22H12.07C14.38 22 16.25 20.5 16.25 17.82V13.9C16.25 13.48 15.91 13.15 15.5 13.15Z" fill="#5F6368"></path>
                            <path d="M17.8198 2H15.8498H14.7598H11.9298C9.66977 2 7.83977 3.44 7.75977 6.01C7.81977 6.01 7.86977 6 7.92977 6H10.7598H11.8498H13.8198C16.1298 6 17.9998 7.5 17.9998 10.18V12.15V14.86V16.83C17.9998 16.89 17.9898 16.94 17.9898 16.99C20.2198 16.92 21.9998 15.44 21.9998 12.83V10.86V8.15V6.18C21.9998 3.5 20.1298 2 17.8198 2Z" fill="#5F6368"></path>
                            <path d="M11.9806 7.15024C11.6706 6.84024 11.1406 7.05024 11.1406 7.48024V10.1002C11.1406 11.2002 12.0706 12.1002 13.2106 12.1002C13.9206 12.1102 14.9106 12.1102 15.7606 12.1102C16.1906 12.1102 16.4106 11.6102 16.1106 11.3102C15.0206 10.2202 13.0806 8.27024 11.9806 7.15024Z" fill="#5F6368"></path>
                        </g>
                    </svg>
                </div>

                <!-- Cut Tool -->
                <div id="cutBtn" class="vertical-tool" style="display: flex; align-items: center; justify-content: center; height: 33.33%; cursor: pointer; transition: all 0.2s;" title="Cut (Ctrl+X)">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 18px; height: 18px;">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <path d="M5.5 10C7.433 10 9 8.433 9 6.5C9 4.567 7.433 3 5.5 3C3.567 3 2 4.567 2 6.5C2 8.433 3.567 10 5.5 10Z" fill="#5F6368"></path>
                            <path d="M5.5 21C7.433 21 9 19.433 9 17.5C9 15.567 7.433 14 5.5 14C3.567 14 2 15.567 2 17.5C2 19.433 3.567 21 5.5 21Z" fill="#5F6368"></path>
                            <path opacity="0.4" d="M15.2491 11.9798L22.4491 6.59978C22.7791 6.34978 22.8491 5.87978 22.5991 5.54978C22.3491 5.21978 21.8791 5.14978 21.5491 5.39978L13.9991 11.0498L8.4491 6.89978C8.1191 6.64978 7.64908 6.71978 7.39908 7.04978C7.14908 7.37978 7.21908 7.84978 7.54908 8.09978L12.7491 11.9898L6.8491 16.4098C6.5191 16.6598 6.4491 17.1298 6.6991 17.4598C6.8491 17.6598 7.06908 17.7598 7.29908 17.7598C7.45908 17.7598 7.60909 17.7098 7.74909 17.6098L14.0091 12.9298L21.5591 18.5798C21.6891 18.6798 21.8491 18.7298 22.0091 18.7298C22.2391 18.7298 22.4591 18.6298 22.6091 18.4298C22.8591 18.0998 22.7891 17.6298 22.4591 17.3798L15.2491 11.9798Z" fill="#5F6368"></path>
                        </g>
                    </svg>
                </div>

                <!-- Clipboard Tool -->
                <div id="clipboardBtn" class="vertical-tool" style="display: flex; align-items: center; justify-content: center; height: 33.33%; cursor: pointer; transition: all 0.2s;" title="Clipboard History">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 18px; height: 18px;">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <path opacity="0.4" d="M16.2391 3.6499H7.75906C5.28906 3.6499 3.28906 5.6599 3.28906 8.1199V17.5299C3.28906 19.9899 5.29906 21.9999 7.75906 21.9999H16.2291C18.6991 21.9999 20.6991 19.9899 20.6991 17.5299V8.1199C20.7091 5.6499 18.6991 3.6499 16.2391 3.6499Z" fill="#5F6368"></path>
                            <path d="M14.3498 2H9.64977C8.60977 2 7.75977 2.84 7.75977 3.88V4.82C7.75977 5.86 8.59977 6.7 9.63977 6.7H14.3498C15.3898 6.7 16.2298 5.86 16.2298 4.82V3.88C16.2398 2.84 15.3898 2 14.3498 2Z" fill="#5F6368"></path>
                            <path d="M15 12.9502H8C7.59 12.9502 7.25 12.6102 7.25 12.2002C7.25 11.7902 7.59 11.4502 8 11.4502H15C15.41 11.4502 15.75 11.7902 15.75 12.2002C15.75 12.6102 15.41 12.9502 15 12.9502Z" fill="#5F6368"></path>
                            <path d="M12.38 16.9502H8C7.59 16.9502 7.25 16.6102 7.25 16.2002C7.25 15.7902 7.59 15.4502 8 15.4502H12.38C12.79 15.4502 13.13 15.7902 13.13 16.2002C13.13 16.6102 12.79 16.9502 12.38 16.9502Z" fill="#5F6368"></path>
                        </g>
                    </svg>
                </div>
            </div>
        </div>

        <span class="toolbar-separator" role="separator"></span>

        <!-- Undo/Redo -->
        <div class="toolbar-group" role="group" aria-label="Undo/Redo">
            <button id="undoBtn" title="Undo (Ctrl+Z)" aria-pressed="false">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 20px; height: 20px;">
                    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                    <g id="SVGRepo_iconCarrier">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M7.53033 3.46967C7.23744 3.17678 6.76256 3.17678 6.46967 3.46967L3.46967 6.46967C3.17678 6.76256 3.17678 7.23744 3.46967 7.53033L6.46967 10.5303C6.76256 10.8232 7.23744 10.8232 7.53033 10.5303C7.82322 10.2374 7.82322 9.76256 7.53033 9.46967L5.06066 7L7.53033 4.53033C7.82322 4.23744 7.82322 3.76256 7.53033 3.46967Z" fill="#5F6368"></path>
                        <path opacity="0.5" d="M5.81066 6.25H15C18.1756 6.25 20.75 8.82436 20.75 12C20.75 15.1756 18.1756 17.75 15 17.75H8C7.58579 17.75 7.25 17.4142 7.25 17C7.25 16.5858 7.58579 16.25 8 16.25H15C17.3472 16.25 19.25 14.3472 19.25 12C19.25 9.65279 17.3472 7.75 15 7.75H5.81066L5.06066 7L5.81066 6.25Z" fill="#5F6368"></path>
                    </g>
                </svg>
            </button>
            <button id="redoBtn" title="Redo (Ctrl+Y)" aria-pressed="false">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 20px; height: 20px;">
                    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                    <g id="SVGRepo_iconCarrier">
                        <path opacity="0.4" d="M16.8691 18.3101H8.86914C6.10914 18.3101 3.86914 16.0701 3.86914 13.3101C3.86914 10.5501 6.10914 8.31006 8.86914 8.31006H19.8691" stroke="#5F6368" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M17.5703 10.8099L20.1303 8.24994L17.5703 5.68994" stroke="#5F6368" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    </g>
                </svg>
            </button>
        </div>

        <span class="toolbar-separator" role="separator"></span>

        <!-- Format Painter -->
        <div class="toolbar-group" role="group" aria-label="Format Painter">
            <button id="formatPainterBtn" title="Format Painter (Ctrl+Shift+C)" aria-pressed="false">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 20px; height: 20px;">
                    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                    <g id="SVGRepo_iconCarrier">
                        <path d="M6.75 6.79904L6.375 7.44856L6.75 6.79904ZM6.20096 6.25L5.55144 6.625L6.20096 6.25ZM17.799 6.25L18.4486 6.625L17.799 6.25ZM17.25 6.79904L17.625 7.44856L17.25 6.79904ZM17.25 2.20096L17.625 1.55144L17.25 2.20096ZM17.799 2.75L18.4486 2.375L17.799 2.75ZM6.75 2.20096L6.375 1.55144L6.75 2.20096ZM6.20096 2.75L5.55144 2.375L6.20096 2.75ZM13.7071 21.7071L13.1768 21.1768L13.7071 21.7071ZM13.7071 14.2929L13.1768 14.8232L13.7071 14.2929ZM10.2929 14.2929L9.76256 13.7626H9.76256L10.2929 14.2929ZM10.2929 21.7071L10.8232 21.1768L10.2929 21.7071ZM12.0047 14.75C12.4189 14.75 12.7547 14.4142 12.7547 14C12.7547 13.5858 12.4189 13.25 12.0047 13.25V14.75ZM8.5 2.75H15.5V1.25H8.5V2.75ZM15.5 6.25H8.5V7.75H15.5V6.25ZM8.5 6.25C8.01889 6.25 7.7082 6.24928 7.47275 6.22794C7.2476 6.20754 7.16586 6.17311 7.125 6.14952L6.375 7.44856C6.68221 7.62593 7.00817 7.69198 7.33735 7.72182C7.65622 7.75072 8.04649 7.75 8.5 7.75V6.25ZM5.25 4.5C5.25 4.95351 5.24928 5.34378 5.27818 5.66265C5.30802 5.99183 5.37407 6.31779 5.55144 6.625L6.85048 5.875C6.82689 5.83414 6.79246 5.7524 6.77206 5.52725C6.75072 5.2918 6.75 4.98111 6.75 4.5H5.25ZM7.125 6.14952C7.01099 6.08369 6.91631 5.98901 6.85048 5.875L5.55144 6.625C5.74892 6.96704 6.03296 7.25108 6.375 7.44856L7.125 6.14952ZM17.25 4.5C17.25 4.98111 17.2493 5.2918 17.2279 5.52725C17.2075 5.7524 17.1731 5.83414 17.1495 5.875L18.4486 6.625C18.6259 6.31779 18.692 5.99183 18.7218 5.66265C18.7507 5.34378 18.75 4.95351 18.75 4.5H17.25ZM15.5 7.75C15.9535 7.75 16.3438 7.75072 16.6627 7.72182C16.9918 7.69198 17.3178 7.62593 17.625 7.44856L16.875 6.14952C16.8341 6.17311 16.7524 6.20754 16.5273 6.22794C16.2918 6.24928 15.9811 6.25 15.5 6.25V7.75ZM17.1495 5.875C17.0837 5.98901 16.989 6.08369 16.875 6.14952L17.625 7.44856C17.967 7.25108 18.2511 6.96704 18.4486 6.625L17.1495 5.875ZM15.5 2.75C15.9811 2.75 16.2918 2.75072 16.5273 2.77206C16.7524 2.79246 16.8341 2.82689 16.875 2.85048L17.625 1.55144C17.3178 1.37407 16.9918 1.30802 16.6627 1.27818C16.3438 1.24928 15.9535 1.25 15.5 1.25V2.75ZM18.75 4.5C18.75 4.04649 18.7507 3.65622 18.7218 3.33735C18.692 3.00817 18.6259 2.68221 18.4486 2.375L17.1495 3.125C17.1731 3.16586 17.2075 3.2476 17.2279 3.47275C17.2493 3.7082 17.25 4.01889 17.25 4.5H18.75ZM16.875 2.85048C16.989 2.91631 17.0837 3.01099 17.1495 3.125L18.4486 2.375C18.2511 2.03296 17.967 1.74892 17.625 1.55144L16.875 2.85048ZM8.5 1.25C8.04649 1.25 7.65622 1.24928 7.33735 1.27818C7.00817 1.30802 6.68221 1.37407 6.375 1.55144L7.125 2.85048C7.16586 2.82689 7.2476 2.79246 7.47275 2.77206C7.7082 2.75072 8.01889 2.75 8.5 2.75V1.25ZM6.75 4.5C6.75 4.01889 6.75072 3.7082 6.77206 3.47275C6.79246 3.2476 6.82689 3.16586 6.85048 3.125L5.55144 2.375C5.37407 2.68221 5.30802 3.00817 5.27818 3.33735C5.24928 3.65622 5.25 4.04649 5.25 4.5H6.75ZM6.375 1.55144C6.03296 1.74892 5.74892 2.03296 5.55144 2.375L6.85048 3.125C6.91631 3.01099 7.01099 2.91631 7.125 2.85048L6.375 1.55144ZM10.75 20V16H9.25V20H10.75ZM13.25 16V20H14.75V20H13.25ZM13.25 20C13.25 20.4926 13.2484 20.7866 13.2201 20.9973C13.2071 21.0939 13.1918 21.1423 13.1828 21.164C13.1808 21.1691 13.1791 21.1724 13.1781 21.1743C13.1771 21.1762 13.1766 21.1771 13.1765 21.1772C13.1765 21.1772 13.1766 21.177 13.1769 21.1766C13.1772 21.1763 13.1772 21.1763 13.1768 21.1768L14.2374 22.2374C14.5465 21.9284 14.6589 21.5527 14.7067 21.1972C14.7516 20.8633 14.75 20.4502 14.75 20H13.25ZM12 22.75C12.4502 22.75 12.8633 22.7516 13.1972 22.7067C13.5527 22.6589 13.9284 22.5465 14.2374 22.2374L13.1768 21.1768C13.1763 21.1772 13.1763 21.1772 13.1767 21.1769C13.177 21.1766 13.1772 21.1765 13.1772 21.1765C13.1771 21.1766 13.1762 21.1771 13.1743 21.1781C13.1724 21.1791 13.1691 21.1808 13.164 21.1828C13.1423 21.1918 13.0939 21.2071 12.9973 21.2201C12.7866 21.2484 12.4926 21.25 12 21.25V22.75ZM12 14.75C12.4926 14.75 12.7866 14.7516 12.9973 14.7799C13.0939 14.7929 13.1423 14.8082 13.164 14.8172C13.1691 14.8192 13.1724 14.8209 13.1743 14.8219C13.1762 14.8229 13.1771 14.8234 13.1772 14.8235C13.1772 14.8235 13.177 14.8234 13.1767 14.8231C13.1763 14.8228 13.1763 14.8228 13.1768 14.8232L14.2374 13.7626C13.9284 13.4535 13.5527 13.3411 13.1972 13.2933C12.8633 13.2484 12.4502 13.25 12 13.25V14.75ZM14.75 16C14.75 15.5498 14.7516 15.1367 14.7067 14.8028C14.6589 14.4473 14.5465 14.0716 14.2374 13.7626L13.1768 14.8232C13.1772 14.8237 13.1772 14.8237 13.1769 14.8234C13.1766 14.823 13.1765 14.8228 13.1765 14.8228C13.1766 14.8229 13.1771 14.8238 13.1781 14.8257C13.1791 14.8276 13.1808 14.8309 13.1828 14.836C13.1918 14.8577 13.2071 14.9061 13.2201 15.0027C13.2484 15.2134 13.25 15.5074 13.25 16H14.75ZM10.75 16C10.75 15.5074 10.7516 15.2134 10.7799 15.0027C10.7929 14.9061 10.8082 14.8577 10.8172 14.836C10.8192 14.8309 10.8209 14.8276 10.8219 14.8257C10.8229 14.8238 10.8234 14.8229 10.8235 14.8228C10.8235 14.8228 10.8234 14.823 10.8231 14.8234C10.8228 14.8237 10.8228 14.8237 10.8232 14.8232L9.76256 13.7626C9.45354 14.0716 9.34109 14.4473 9.2933 14.8028C9.24841 15.1367 9.25 15.5498 9.25 16H10.75ZM12 13.25C11.5498 13.25 11.1367 13.2484 10.8028 13.2933C10.4473 13.3411 10.0716 13.4535 9.76256 13.7626L10.8232 14.8232C10.8237 14.8228 10.8237 14.8228 10.8234 14.8231C10.823 14.8234 10.8228 14.8235 10.8228 14.8235C10.8229 14.8234 10.8238 14.8229 10.8257 14.8219C10.8276 14.8209 10.8309 14.8192 10.836 14.8172C10.8577 14.8082 10.9061 14.7929 11.0027 14.7799C11.2134 14.7516 11.5074 14.75 12 14.75V13.25ZM9.25 20C9.25 20.4502 9.24841 20.8633 9.2933 21.1972C9.34109 21.5527 9.45354 21.9284 9.76256 22.2374L10.8232 21.1768C10.8228 21.1763 10.8228 21.1763 10.8231 21.1766C10.8234 21.177 10.8235 21.1772 10.8235 21.1772C10.8234 21.1771 10.8229 21.1762 10.8219 21.1743C10.8209 21.1724 10.8192 21.1691 10.8172 21.164C10.8082 21.1423 10.7929 21.0939 10.7799 20.9973C10.7516 20.7866 10.75 20.4926 10.75 20H9.25ZM12 21.25C11.5074 21.25 11.2134 21.2484 11.0027 21.2201C10.9061 21.2071 10.8577 21.1918 10.836 21.1828C10.8309 21.1808 10.8276 21.1791 10.8257 21.1781C10.8238 21.1771 10.8229 21.1766 10.8228 21.1765C10.8228 21.1765 10.823 21.1766 10.8234 21.1769C10.8237 21.1772 10.8237 21.1772 10.8232 21.1768L9.76256 22.2374C10.0716 22.5465 10.4473 22.6589 10.8028 22.7067C11.1367 22.7516 11.5498 22.75 12 22.75V21.25ZM12 14.75H12.0047V13.25H12V14.75Z" fill="#5F6368"></path>
                        <path opacity="0.5" d="M5.5 3.75C5.08579 3.75 4.75 4.08579 4.75 4.5C4.75 4.91421 5.08579 5.25 5.5 5.25V3.75ZM6 3.75H5.5V5.25H6V3.75Z" fill="#5F6368"></path>
                        <path opacity="0.5" d="M15.4068 10.989L15.2956 10.2473L15.4068 10.989ZM19.4834 10.3775L19.3722 9.63581L19.4834 10.3775ZM21.8611 5.76733L22.559 5.49258L22.559 5.49258L21.8611 5.76733ZM20.7329 4.63903L20.4581 5.3369L20.7329 4.63903ZM20.9386 10.0438L20.5867 9.38148V9.38148L20.9386 10.0438ZM21.886 8.94369L22.5932 9.19346V9.19346L21.886 8.94369ZM12.4847 11.9173L11.9164 11.4278L11.9164 11.4278L12.4847 11.9173ZM11.255 13.9875C11.2481 14.4016 11.5782 14.743 11.9923 14.7499C12.4065 14.7568 12.7479 14.4267 12.7548 14.0125L11.255 13.9875ZM15.5181 11.7307L19.5947 11.1192L19.3722 9.63581L15.2956 10.2473L15.5181 11.7307ZM19.0451 3.75H18.0002V5.25H19.0451V3.75ZM22.7502 7.4551C22.7502 7.02002 22.7506 6.65783 22.7312 6.3612C22.7115 6.05823 22.6689 5.77171 22.559 5.49258L21.1633 6.04208C21.1924 6.11609 21.2194 6.22858 21.2344 6.45878C21.2498 6.6953 21.2502 7.00044 21.2502 7.4551H22.7502ZM19.0451 5.25C19.4997 5.25 19.8049 5.25037 20.0414 5.26579C20.2716 5.2808 20.3841 5.30776 20.4581 5.3369L21.0076 3.94117C20.7285 3.83128 20.442 3.78872 20.139 3.76897C19.8423 3.74963 19.4802 3.75 19.0451 3.75V5.25ZM22.559 5.49258C22.2795 4.78261 21.7176 4.22069 21.0076 3.94117L20.4581 5.3369C20.7808 5.46395 21.0362 5.71937 21.1633 6.04208L22.559 5.49258ZM19.5947 11.1192C20.3032 11.0129 20.8462 10.9422 21.2905 10.7061L20.5867 9.38148C20.4256 9.4671 20.2002 9.5116 19.3722 9.63581L19.5947 11.1192ZM21.2502 7.4551C21.2502 8.29243 21.2396 8.52185 21.1788 8.69391L22.5932 9.19346C22.7608 8.71904 22.7502 8.17157 22.7502 7.4551H21.2502ZM21.2905 10.7061C21.8989 10.3829 22.3638 9.84304 22.5932 9.19346L21.1788 8.69391C21.0745 8.98918 20.8632 9.23455 20.5867 9.38148L21.2905 10.7061ZM15.2956 10.2473C14.5023 10.3663 13.8378 10.4646 13.315 10.6116C12.772 10.7643 12.2915 10.9923 11.9164 11.4278L13.053 12.4067C13.1625 12.2796 13.3305 12.1654 13.7211 12.0556C14.132 11.94 14.6863 11.8555 15.5181 11.7307L15.2956 10.2473ZM12.7548 14.0125C12.7726 12.9469 12.8715 12.6175 13.053 12.4067L11.9164 11.4278C11.3356 12.1023 11.2719 12.9787 11.255 13.9875L12.7548 14.0125Z" fill="#5F6368"></path>
                    </g>
                </svg>
            </button>
        </div>

        <span class="toolbar-separator" role="separator"></span>

        <!-- Text Formatting -->
        <div class="toolbar-group" role="group" aria-label="Text Formatting">
            <button id="boldBtn" title="Bold (Ctrl+B)" aria-pressed="false"><span class="material-icons">format_bold</span></button>
            <button id="italicBtn" title="Italic (Ctrl+I)" aria-pressed="false"><span class="material-icons">format_italic</span></button>
            <button id="underlineBtn" title="Underline (Ctrl+U)" aria-pressed="false"><span class="material-icons">format_underlined</span></button>
        </div>

        <span class="toolbar-separator" role="separator"></span>

        <!-- Colors -->
        <div class="toolbar-group" role="group" aria-label="Colors">
            <label for="fontColorPicker" class="color-picker-label" title="Click for color palette">
                <span class="material-icons">format_color_text</span>
            </label>
            <input type="color" id="fontColorPicker" title="Font Color" value="#000000" aria-label="Choose font color">
            <label for="bgColorPicker" class="color-picker-label" title="Click for color palette">
                <span class="material-icons">format_color_fill</span>
            </label>
            <input type="color" id="bgColorPicker" title="Background Color" value="#ffffff" aria-label="Choose background color">
        </div>

        <span class="toolbar-separator" role="separator"></span>

        <!-- Font Size -->
        <div class="toolbar-group" role="group" aria-label="Font Size">
            <button id="decreaseFontSizeBtn" title="Decrease Font Size (Ctrl+[)"><span class="material-icons">text_decrease</span></button>
            <button id="increaseFontSizeBtn" title="Increase Font Size (Ctrl+])"><span class="material-icons">text_increase</span></button>
            <select id="fontSizeSelect" title="Font Size" aria-label="Select font size">
                <option value="6">6</option>
                <option value="8">8</option>
                <option value="9">9</option>
                <option value="10">10</option>
                <option value="11" selected>11</option>
                <option value="12">12</option>
                <option value="14">14</option>
                <option value="16">16</option>
                <option value="18">18</option>
                <option value="20">20</option>
                <option value="24">24</option>
                <option value="28">28</option>
                <option value="36">36</option>
                <option value="48">48</option>
                <option value="72">72</option>
            </select>
        </div>

        <span class="toolbar-separator" role="separator"></span>

        <!-- Font and Number Format -->
        <div class="toolbar-group" role="group" aria-label="Font and Number Format">
            <select id="fontFamilySelect" title="Font Family" aria-label="Select font family">
                <option value="Arial">Arial</option>
                <option value="Calibri">Calibri</option>
                <option value="Times New Roman">Times New Roman</option>
                <option value="Verdana">Verdana</option>
                <option value="Helvetica">Helvetica</option>
                <option value="Tahoma">Tahoma</option>
                <option value="Georgia">Georgia</option>
                <option value="Courier New">Courier New</option>
                <option value="Trebuchet MS">Trebuchet MS</option>
                <option value="Segoe UI">Segoe UI</option>
                <option value="Open Sans">Open Sans</option>
                <option value="Roboto">Roboto</option>
                <option value="Lato">Lato</option>
                <option value="Montserrat">Montserrat</option>
            </select>
            <select id="numberFormatSelect" title="Number Format" aria-label="Select number format">
                <option value="General">General</option>
                <option value="0">0</option>
                <option value="0.00">0.00</option>
                <option value="#,##0">#,##0</option>
                <option value="#,##0.00">#,##0.00</option>
                <option value="$#,##0.00">Currency ($)</option>
                <option value="0%">Percentage</option>
                <option value="0.00%">Percentage (2 decimals)</option>
                <option value="m/d/yy">Short Date</option>
                <option value="dddd, mmmm dd, yyyy">Long Date</option>
                <option value="h:mm AM/PM">Time</option>
            </select>
        </div>

        <span class="toolbar-separator" role="separator"></span>

        <!-- Alignment -->
        <div class="toolbar-group" role="group" aria-label="Text Alignment">
            <button id="alignLeftBtn" title="Align Left (Ctrl+L)" aria-pressed="false"><span class="material-icons">format_align_left</span></button>
            <button id="alignCenterBtn" title="Align Center (Ctrl+E)" aria-pressed="false"><span class="material-icons">format_align_center</span></button>
            <button id="alignRightBtn" title="Align Right (Ctrl+R)" aria-pressed="false"><span class="material-icons">format_align_right</span></button>
        </div>

        <span class="toolbar-separator" role="separator"></span>

        <!-- Merge/Unmerge -->
        <div class="toolbar-group" role="group" aria-label="Merge Operations">
            <button id="mergeBtn" title="Merge Selected Cells (Ctrl+M)"><span class="material-icons">merge_type</span></button>
            <button id="unmergeBtn" title="Unmerge Cells in Selection (Ctrl+Shift+M)"><span class="material-icons">call_split</span></button>
        </div>

        <span class="toolbar-separator" role="separator"></span>

        <!-- Row Operations -->
        <div class="toolbar-group" role="group" aria-label="Row Operations">
            <button id="insertRowAboveBtn" title="Insert Row Above (Ctrl+Alt+Up)"><span class="material-icons">north</span></button>
            <button id="insertRowBelowBtn" title="Insert Row Below (Ctrl+Alt+Down)"><span class="material-icons">south</span></button>
            <button id="deleteSelectedRowsBtn" title="Delete Selected Row(s) (Ctrl+Alt+Minus)"><span class="material-icons">remove_circle_outline</span></button>
        </div>

        <span class="toolbar-separator" role="separator"></span>

        <!-- Column Operations -->
        <div class="toolbar-group" role="group" aria-label="Column Operations">
            <button id="insertColLeftBtn" title="Insert Column Left (Ctrl+Alt+Left)"><span class="material-icons">west</span></button>
            <button id="insertColRightBtn" title="Insert Column Right (Ctrl+Alt+Right)"><span class="material-icons">east</span></button>
            <button id="deleteSelectedColsBtn" title="Delete Selected Column(s) (Ctrl+Alt+Minus)"><span class="material-icons">delete_outline</span></button>
        </div>

        <span class="toolbar-separator" role="separator"></span>

        <!-- Data Operations -->
        <div class="toolbar-group" role="group" aria-label="Data Operations">
            <button id="sortAscBtn" title="Sort Ascending (Alt+Up)"><span class="material-icons">sort_by_alpha</span></button>
            <button id="filterBtn" title="Toggle Filter (Ctrl+Shift+L)" aria-pressed="false"><span class="material-icons">filter_alt</span></button>
            <button id="formulaBtn" title="Insert Formula (Alt+F)"><span class="material-icons">functions</span></button>
            <button id="insertChartBtn" title="Insert Chart" class="primary-button"><span class="material-icons">bar_chart</span> Chart</button>
        </div>

        <span class="toolbar-separator" role="separator"></span>

        <!-- Print -->
        <div class="toolbar-group" role="group" aria-label="Print Operations">
            <button id="printSheetBtn" title="Print Sheet (Ctrl+P)"><span class="material-icons">print</span></button>
        </div>

        <span class="toolbar-separator" role="separator"></span>

        <!-- Security -->
        <div class="toolbar-group" role="group" aria-label="Security Operations">
            <button id="lockFileBtn" title="Password Protect File" onclick="if(window.showPasswordProtectionModal) { window.showPasswordProtectionModal(); } else { alert('Password protection feature is not available'); }"><span class="material-icons">lock</span></button>
        </div>
    </div>

    <div id="spreadsheetContainer">
        <!-- Welcome Modal -->
        <div id="welcomeModal" class="welcome-overlay">
            <div class="welcome-modal">
                <div class="welcome-header">
                    <div class="welcome-logo">
                        <span class="material-icons" style="font-size: 36px; color: #1a73e8;">table_chart</span>
                    </div>
                    <h1>Welcome to Enven Bridge Sheet</h1>
                    <p>Your powerful spreadsheet solution for data analysis and visualization</p>
                </div>

                <div class="welcome-content">
                    <p>Get started by creating a new workbook or opening an existing Excel file from your device.</p>

                    <div class="welcome-actions">
                        <button id="welcomeNewBtn" class="welcome-action-btn primary">
                            <span class="material-icons">add_circle</span>
                            <span>New Workbook</span>
                        </button>

                        <button id="welcomeOpenBtn" class="welcome-action-btn">
                            <span class="material-icons">folder_open</span>
                            <span>Open File</span>
                        </button>
                    </div>

                    <div class="recent-files">
                        <h3>Recent Files</h3>
                        <!-- Recent files will be loaded dynamically -->
                        <div class="no-recent-files">Loading recent files...</div>
                    </div>
                </div>

                <div class="welcome-footer">
                    <p style="text-align: center; width: 100%; display: block;">Enven Bridge Sheet v1.0.0 | &copy; 2025 Enven Bridge</p>
                </div>
            </div>
        </div>
    </div>

    <div id="formulaBar" style="display: none;">
        <span class="material-icons">functions</span>
        <input type="text" id="formulaInput" placeholder="Enter formula...">
    </div>

    <!-- Sheet Tabs Container -->
    <div id="sheetTabsContainer" class="sheet-tabs-container">
        <!-- Sheet tabs will be dynamically added here -->
        <div class="add-sheet-btn" id="addSheetBtn" title="Add Sheet">
            <span class="material-icons">add</span>
        </div>
    </div>

    <p id="status"></p>

    <!-- jQuery (required by some libraries) -->
    <script src="lib/jquery-3.6.0.min.js"></script>

    <!-- Excel Libraries -->
    <script src="lib/xlsx-populate.min.js"></script>
    <script src="lib/xlsx.full.min.js"></script>
    <script src="lib/exceljs.min.js"></script>
    <!-- Removed ESM module that was causing issues -->
    <!-- <script src="lib/luckysheet.esm.min.js"></script> -->
    <!-- Fixed jQuery dependency issue -->
    <script src="js/jquery-fix.js"></script>
    <script src="lib/jquery.handsontable.full.min.js"></script>

    <!-- Chart Libraries -->
    <script src="lib/apexcharts.min.js"></script>
    <script src="lib/echarts.js"></script>
    <script src="lib/d3.min.js"></script>
    <!-- Chart library fix to ensure global availability -->
    <script src="js/chart-library-fix.js"></script>
    <!-- Improved Chart System -->
    <script src="js/chart-storage.js" type="module"></script>
    <script src="js/chrome-storage-integration.js" type="module"></script>
    <script src="js/chart-renderer.js" type="module"></script>
    <script src="js/improved-chart-data-processor.js" type="module"></script>
    <script src="js/improved-chart-manager.js" type="module"></script>

    <!-- Grid Libraries -->
    <script src="lib/gridjs.umd.js"></script>
    <script src="lib/gridstack.all.js"></script>
    <link rel="stylesheet" href="lib/gridstack.min.css">

    <!-- Date and Time Libraries -->
    <script src="lib/dayjs.min.js"></script>

    <!-- Editor Libraries - Fixed require/define issues -->
    <script src="js/polyfills.js"></script>
    <script src="lib/editor.main.js"></script>
    <script src="lib/tiptap.min.js"></script>

    <!-- Utility Libraries -->
    <script src="lib/papaparse.min.js"></script>
    <script src="lib/pdf-lib.min.js"></script>
    <script src="lib/tesseract.min.js"></script>
    <script src="lib/intro.min.js"></script>
    <script src="lib/motion.js"></script>
    <script src="lib/html2canvas.min.js"></script>
    <script src="lib/Sortable.min.js"></script>

    <!-- Welcome Modal Fix Scripts -->
    <script src="js/welcome-fix.js"></script>
    <script src="js/welcome-modal-fix.js"></script>

    <!-- Remove test button script -->
    <script src="js/remove-test-button.js"></script>

    <!-- Color Palette Script - Load before app-init.js -->
    <script type="module" src="js/color-palette.js"></script>

    <!-- Application Scripts -->
    <script src="js/app-init.js"></script>
    <!-- Modules are loaded dynamically by app-init.js -->
    <!-- <script type="module" src="js/main_script.js"></script> -->
    <!-- <script type="module" src="js/menu_manager.js"></script> -->
    <!-- <script type="module" src="js/panel_manager.js"></script> -->
    <!-- <script type="module" src="js/chart_manager.js"></script> -->
    <script type="module" src="js/clipboard_manager.js"></script>
    <script type="module" src="js/improved_cell_resizer.js"></script>
    <script type="module" src="js/spreadsheet-integration.js"></script>
    <script type="module" src="js/acumatica-connector.js"></script>

    <!-- Chart Creation Dialog -->
    <script type="module" src="js/chart-creation-dialog.js"></script>

    <!-- Chart System Test -->
    <script src="js/chart-test.js"></script>
</body>
</html>