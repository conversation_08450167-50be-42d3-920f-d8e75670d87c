/**
 * Chart System Test
 * Tests the chart creation functionality
 */

// Test function to verify chart system
function testChartSystem() {
    console.log('Testing chart system...');
    
    // Check if chart libraries are loaded
    const hasApex = typeof window.ApexCharts !== 'undefined';
    const hasECharts = typeof window.echarts !== 'undefined';
    const hasD3 = typeof window.d3 !== 'undefined';
    
    console.log('Chart libraries status:', {
        ApexCharts: hasApex,
        ECharts: hasECharts,
        D3: hasD3
    });
    
    // Test data
    const testRange = {
        start: { r: 1, c: 1 },
        end: { r: 5, c: 3 }
    };
    
    // Test chart creation dialog
    if (window.showChartCreationDialog) {
        console.log('Chart creation dialog function available');
        
        // Create test button
        const testButton = document.createElement('button');
        testButton.textContent = 'Test Chart Creation';
        testButton.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 10000;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        `;
        
        testButton.addEventListener('click', () => {
            console.log('Testing chart creation with sample data...');
            
            // Create sample data for testing
            window.currentSheet = {
                cell: (r, c) => ({
                    value: () => {
                        if (r === 1 && c === 1) return 'Category';
                        if (r === 1 && c === 2) return 'Values';
                        if (r === 2 && c === 1) return 'A';
                        if (r === 2 && c === 2) return 10;
                        if (r === 3 && c === 1) return 'B';
                        if (r === 3 && c === 2) return 20;
                        if (r === 4 && c === 1) return 'C';
                        if (r === 4 && c === 2) return 15;
                        if (r === 5 && c === 1) return 'D';
                        if (r === 5 && c === 2) return 25;
                        return '';
                    }
                })
            };
            
            // Test the chart creation dialog
            try {
                window.showChartCreationDialog(testRange);
            } catch (error) {
                console.error('Error testing chart creation:', error);
                alert('Error testing chart creation: ' + error.message);
            }
        });
        
        document.body.appendChild(testButton);
        console.log('Test button added to page');
    } else {
        console.error('Chart creation dialog function not available');
    }
}

// Test when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', testChartSystem);
} else {
    testChartSystem();
}

// Also test after a delay to ensure all scripts are loaded
setTimeout(testChartSystem, 2000);

// Export for manual testing
window.testChartSystem = testChartSystem;
