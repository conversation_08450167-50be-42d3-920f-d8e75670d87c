/* Chart Creation Dialog Styles */

.chart-creation-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.chart-creation-modal-overlay.show {
    opacity: 1;
}

.chart-creation-modal {
    background: white;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 900px;
    height: 80%;
    max-height: 700px;
    display: flex;
    flex-direction: column;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.chart-creation-modal-overlay.show .chart-creation-modal {
    transform: scale(1);
}

.chart-modal-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.chart-modal-header h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: #333;
}

.chart-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.chart-modal-close:hover {
    background: #e9ecef;
    color: #333;
}

.chart-modal-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chart-modal-tabs {
    display: flex;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
}

.chart-tab {
    padding: 12px 24px;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.chart-tab:hover {
    background: #e9ecef;
    color: #333;
}

.chart-tab.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background: white;
}

.chart-modal-panels {
    flex: 1;
    overflow-y: auto;
}

.chart-panel {
    display: none;
    padding: 20px;
    height: 100%;
    overflow-y: auto;
}

.chart-panel.active {
    display: block;
}

.chart-panel h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

/* Chart Type Selection */
.chart-type-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.chart-type-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    text-align: center;
}

.chart-type-option:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
    transform: translateY(-2px);
}

.chart-type-option.selected {
    border-color: #007bff;
    background: #f8f9ff;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2);
}

.chart-type-icon {
    font-size: 32px;
    margin-bottom: 8px;
}

.chart-type-name {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.chart-type-description {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #007bff;
}

/* Data Configuration */
.data-config-section {
    margin-bottom: 20px;
}

.data-config-section label {
    display: block;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
}

.data-range-input {
    display: flex;
    gap: 10px;
    align-items: center;
}

.data-range-input input {
    flex: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.chart-btn-small {
    padding: 8px 16px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.2s ease;
}

.chart-btn-small:hover {
    background: #0056b3;
}

.data-config-section input[type="text"] {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.data-config-section input[type="checkbox"] {
    margin-right: 8px;
}

.data-preview {
    margin-top: 20px;
}

.data-preview h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.data-preview-table {
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.data-preview-table-inner {
    width: 100%;
    border-collapse: collapse;
}

.data-preview-table-inner td {
    padding: 8px 12px;
    border: 1px solid #eee;
    font-size: 12px;
}

.data-preview-table-inner .header-cell {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.data-preview-table-inner .data-cell {
    background: white;
    color: #666;
}

.data-preview-note {
    margin: 10px 0 0 0;
    font-size: 12px;
    color: #666;
    font-style: italic;
}

/* Style Configuration */
.style-config-section {
    margin-bottom: 20px;
}

.style-config-section label {
    display: block;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
}

.color-scheme-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
}

.color-scheme-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.color-scheme-option:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.color-scheme-option.selected {
    border-color: #007bff;
    background: #f8f9ff;
}

.color-scheme-preview {
    display: flex;
    gap: 4px;
    margin-bottom: 8px;
}

.color-dot {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 1px solid #ddd;
}

.color-scheme-name {
    font-size: 12px;
    font-weight: 500;
    color: #333;
    text-transform: capitalize;
}

.style-config-section select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background: white;
}

.style-config-section input[type="checkbox"] {
    margin-right: 8px;
}

/* Preview Panel */
.chart-preview-container {
    border: 1px solid #ddd;
    border-radius: 6px;
    background: #f8f9fa;
    margin-bottom: 20px;
}

#chartPreviewArea {
    min-height: 300px;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-preview-placeholder {
    text-align: center;
    color: #666;
    font-style: italic;
}

.chart-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    font-style: italic;
}

.chart-error {
    text-align: center;
    color: #dc3545;
    padding: 20px;
}

.chart-error p {
    margin: 0 0 5px 0;
    font-weight: 600;
}

.chart-error small {
    font-size: 12px;
    opacity: 0.8;
}

.chart-preview-controls {
    padding: 15px;
    border-top: 1px solid #ddd;
    background: white;
    text-align: center;
}

/* Modal Footer */
.chart-modal-footer {
    padding: 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    background: #f8f9fa;
}

.chart-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.chart-btn-secondary {
    background: #6c757d;
    color: white;
}

.chart-btn-secondary:hover {
    background: #5a6268;
}

.chart-btn-primary {
    background: #007bff;
    color: white;
}

.chart-btn-primary:hover {
    background: #0056b3;
}

/* Chart Container Styles */
.chart-container {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.chart-container .chart-header:hover {
    background: #e9ecef;
}

.chart-container .chart-btn-small:hover {
    background: #e9ecef;
}

/* Chart Container Styles */
.chart-container {
    position: absolute !important;
    background: white !important;
    border: 1px solid #ddd !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    overflow: hidden !important;
    resize: both !important;
    min-width: 300px !important;
    min-height: 200px !important;
    box-sizing: border-box !important;
    z-index: 1000 !important;
}

.chart-container .chart-header {
    background: #f8f9fa !important;
    border-bottom: 1px solid #ddd !important;
    padding: 8px 12px !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    cursor: move !important;
    user-select: none !important;
}

.chart-container .chart-content {
    padding: 10px !important;
    height: calc(100% - 40px) !important;
    overflow: hidden !important;
    box-sizing: border-box !important;
}

.chart-container .chart-content > div {
    width: 100% !important;
    height: 100% !important;
    min-width: 280px !important;
    min-height: 180px !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .chart-creation-modal {
        width: 95%;
        height: 90%;
    }

    .chart-type-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 10px;
    }

    .color-scheme-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }

    .chart-modal-header {
        padding: 15px;
    }

    .chart-panel {
        padding: 15px;
    }

    .chart-container {
        width: 90% !important;
        height: 300px !important;
        position: relative !important;
        left: 5% !important;
        top: 50px !important;
    }
}
