/**
 * Chart Test Fix
 * This script provides debugging and testing functionality for the chart system
 */

// Add a test button to verify chart functionality
function addChartTestButton() {
    // Remove existing test button if it exists
    const existingBtn = document.getElementById('chartTestBtn');
    if (existingBtn) {
        existingBtn.remove();
    }

    // Create test button
    const testBtn = document.createElement('button');
    testBtn.id = 'chartTestBtn';
    testBtn.textContent = 'Test Chart System';
    testBtn.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 10000;
        padding: 10px 15px;
        background: #4CAF50;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
    `;

    testBtn.addEventListener('click', testChartSystem);
    document.body.appendChild(testBtn);
}

// Test the chart system
async function testChartSystem() {
    console.log('=== Chart System Test ===');
    
    // Test 1: Check if chart libraries are loaded
    console.log('1. Testing chart library availability:');
    const hasApex = typeof window.ApexCharts !== 'undefined';
    const hasECharts = typeof window.echarts !== 'undefined';
    const hasD3 = typeof window.d3 !== 'undefined';
    
    console.log('- ApexCharts:', hasApex);
    console.log('- ECharts:', hasECharts);
    console.log('- D3:', hasD3);
    
    if (!hasApex && !hasECharts && !hasD3) {
        console.error('No chart libraries available!');
        alert('No chart libraries are loaded. Please check the library loading.');
        return;
    }
    
    // Test 2: Create a test chart with sample data
    console.log('2. Creating test chart with sample data...');
    
    const testRange = {
        start: { r: 1, c: 1 },
        end: { r: 4, c: 3 }
    };
    
    const testData = {
        isValid: true,
        categories: ['Q1', 'Q2', 'Q3', 'Q4'],
        series: [
            { name: 'Sales', data: [100, 120, 140, 110] },
            { name: 'Profit', data: [20, 25, 30, 22] }
        ],
        pieData: {
            labels: ['Q1', 'Q2', 'Q3', 'Q4'],
            values: [100, 120, 140, 110]
        },
        hasHeaders: true,
        hasCategories: true
    };
    
    try {
        // Import the chart creation dialog
        const module = await import('./chart-creation-dialog.js');
        
        // Create a test dialog
        const dialog = new module.ChartCreationDialog(testRange, testData);
        dialog.show();
        
        console.log('Test chart dialog created successfully');
        
    } catch (error) {
        console.error('Error creating test chart:', error);
        alert('Error creating test chart: ' + error.message);
    }
}

// Test chart creation directly
async function testDirectChartCreation() {
    console.log('=== Direct Chart Creation Test ===');
    
    // Create a test container
    const testContainer = document.createElement('div');
    testContainer.id = 'test-chart-container';
    testContainer.style.cssText = `
        position: fixed;
        top: 50px;
        left: 50px;
        width: 500px;
        height: 400px;
        background: white;
        border: 2px solid #333;
        z-index: 9999;
        padding: 20px;
        box-sizing: border-box;
    `;
    
    const closeBtn = document.createElement('button');
    closeBtn.textContent = 'Close Test';
    closeBtn.style.cssText = `
        position: absolute;
        top: 5px;
        right: 5px;
        padding: 5px 10px;
        background: #f44336;
        color: white;
        border: none;
        border-radius: 3px;
        cursor: pointer;
    `;
    closeBtn.addEventListener('click', () => testContainer.remove());
    
    const chartDiv = document.createElement('div');
    chartDiv.style.cssText = `
        width: 100%;
        height: calc(100% - 40px);
        margin-top: 30px;
    `;
    
    testContainer.appendChild(closeBtn);
    testContainer.appendChild(chartDiv);
    document.body.appendChild(testContainer);
    
    // Test data
    const testData = {
        categories: ['Jan', 'Feb', 'Mar', 'Apr'],
        series: [{ name: 'Test Series', data: [10, 20, 15, 25] }]
    };
    
    const options = {
        title: 'Test Chart',
        colors: ['#008FFB', '#00E396', '#FEB019', '#FF4560'],
        showLegend: true,
        showGrid: true,
        enableAnimations: true,
        library: 'auto'
    };
    
    // Try to create chart
    try {
        // Wait for container to be in DOM
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // Force dimensions
        if (chartDiv.clientWidth === 0) {
            chartDiv.style.width = '460px';
            chartDiv.style.height = '320px';
        }
        
        console.log('Chart container dimensions:', {
            width: chartDiv.clientWidth,
            height: chartDiv.clientHeight
        });
        
        // Try ApexCharts first
        if (typeof window.ApexCharts !== 'undefined') {
            console.log('Testing ApexCharts...');
            
            const chartOptions = {
                chart: {
                    type: 'column',
                    height: chartDiv.clientHeight || 320,
                    width: '100%'
                },
                title: {
                    text: options.title,
                    align: 'center'
                },
                colors: options.colors,
                xaxis: {
                    categories: testData.categories
                },
                series: testData.series
            };
            
            const chart = new ApexCharts(chartDiv, chartOptions);
            await chart.render();
            
            console.log('ApexCharts test successful!');
            
        } else if (typeof window.echarts !== 'undefined') {
            console.log('Testing ECharts...');
            
            const chart = echarts.init(chartDiv);
            const chartOptions = {
                title: {
                    text: options.title,
                    left: 'center'
                },
                xAxis: {
                    type: 'category',
                    data: testData.categories
                },
                yAxis: {
                    type: 'value'
                },
                series: [{
                    name: testData.series[0].name,
                    type: 'bar',
                    data: testData.series[0].data
                }]
            };
            
            chart.setOption(chartOptions);
            console.log('ECharts test successful!');
            
        } else {
            console.log('No chart libraries available for testing');
            chartDiv.innerHTML = '<p style="text-align: center; padding: 50px;">No chart libraries available</p>';
        }
        
    } catch (error) {
        console.error('Direct chart creation test failed:', error);
        chartDiv.innerHTML = `<p style="color: red; text-align: center; padding: 50px;">Error: ${error.message}</p>`;
    }
}

// Add test functions to window for console access
window.testChartSystem = testChartSystem;
window.testDirectChartCreation = testDirectChartCreation;

// Auto-add test button when script loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', addChartTestButton);
} else {
    addChartTestButton();
}

console.log('Chart test utilities loaded. Use testChartSystem() or testDirectChartCreation() in console, or click the test button.');
